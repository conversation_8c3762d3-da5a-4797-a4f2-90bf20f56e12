import React from 'react';
import { Button } from '@ui-kitten/components';
import { Platform, Share, Alert } from 'react-native';
import CopyButton from '~components/HouseKeepingComponents/Shared/CopyButton';

const COPY_TIMEOUT = 5000;

type Props = {
  url: string;
  disabled: boolean;
  onClickShare: () => void;
  shared: boolean;
};

const SharePDFButton = ({ url, disabled, onClickShare, shared }: Props) => {
  if (Platform.OS !== 'web' ) {
    const onShare = async () => {
      try {
        await Share.share({
          title: 'PDF Report',
          message: 'PDF Report',
          url,
        });
        onClickShare();
      } catch (error: unknown) {
        if (error instanceof Error) {
          Alert.alert(error.message);
        }
      }
    };

    return (
      <Button
        size="large"
        style={{ width: '100%' }}
        onPress={onShare}
        disabled={disabled}
        appearance={shared ? 'outline' : 'filled'}
        status="info"
      >
        Share PDF Report
      </Button>
    );
  }

  return (
    <CopyButton
      href={url}
      timeout={COPY_TIMEOUT}
      onClickShare={onClickShare}
      disabled={disabled}
      copyText="Share PDF Report"
      copiedText="Link Copied to Clipboard"
      size="large"
      shareIcons={false}
    />
  );
};

export default SharePDFButton;
